const Transaction = require('../models/Transaction');
const Product = require('../models/Product');
const Store = require('../models/Store');

/**
 * Get comprehensive analytics for a store
 */
exports.getStoreAnalytics = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { timeframe = 'today' } = req.query;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    // Calculate date range based on timeframe
    const now = new Date();
    let startDate, previousStartDate;
    
    switch (timeframe) {
      case 'today':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        previousStartDate = new Date(startDate.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'yesterday':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1);
        previousStartDate = new Date(startDate.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        previousStartDate = new Date(startDate.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        previousStartDate = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        break;
      case 'quarter':
        const quarter = Math.floor(now.getMonth() / 3);
        startDate = new Date(now.getFullYear(), quarter * 3, 1);
        previousStartDate = new Date(now.getFullYear(), (quarter - 1) * 3, 1);
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        previousStartDate = new Date(startDate.getTime() - 24 * 60 * 60 * 1000);
    }

    // Get current period data
    const currentPeriodData = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$amount' },
          totalTransactions: { $sum: 1 },
          averageOrder: { $avg: '$amount' }
        }
      }
    ]);

    // Get previous period data for comparison
    const previousPeriodData = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: previousStartDate, $lt: startDate }
        }
      },
      {
        $group: {
          _id: null,
          totalRevenue: { $sum: '$amount' },
          totalTransactions: { $sum: 1 },
          averageOrder: { $avg: '$amount' }
        }
      }
    ]);

    const current = currentPeriodData[0] || { totalRevenue: 0, totalTransactions: 0, averageOrder: 0 };
    const previous = previousPeriodData[0] || { totalRevenue: 0, totalTransactions: 0, averageOrder: 0 };

    // Calculate percentage changes
    const revenueChange = previous.totalRevenue > 0 
      ? ((current.totalRevenue - previous.totalRevenue) / previous.totalRevenue * 100).toFixed(1)
      : 0;
    
    const transactionChange = previous.totalTransactions > 0
      ? ((current.totalTransactions - previous.totalTransactions) / previous.totalTransactions * 100).toFixed(1)
      : 0;
    
    const averageOrderChange = previous.averageOrder > 0
      ? ((current.averageOrder - previous.averageOrder) / previous.averageOrder * 100).toFixed(1)
      : 0;

    // Get revenue trend data
    const revenueData = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            hour: { $hour: '$createdAt' },
            day: { $dayOfMonth: '$createdAt' }
          },
          revenue: { $sum: '$amount' }
        }
      },
      {
        $project: {
          time: {
            $concat: [
              { $toString: '$_id.day' },
              '/',
              { $toString: '$_id.hour' },
              ':00'
            ]
          },
          revenue: 1
        }
      },
      { $sort: { '_id.day': 1, '_id.hour': 1 } }
    ]);

    // Get payment method breakdown
    const paymentMethods = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$payfast.paymentMethod',
          count: { $sum: 1 },
          value: { $sum: '$amount' }
        }
      },
      {
        $project: {
          name: { $ifNull: ['$_id', 'Unknown'] },
          count: 1,
          value: 1
        }
      }
    ]);

    // Get peak hours data
    const peakHours = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: { $hour: '$createdAt' },
          transactions: { $sum: 1 }
        }
      },
      {
        $project: {
          hour: '$_id',
          transactions: 1
        }
      },
      { $sort: { hour: 1 } }
    ]);

    // Get top products
    const topProducts = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      { $unwind: '$items' },
      {
        $group: {
          _id: '$items.name',
          quantity: { $sum: '$items.quantity' },
          revenue: { $sum: { $multiply: ['$items.price', '$items.quantity'] } }
        }
      },
      {
        $project: {
          name: '$_id',
          quantity: 1,
          revenue: 1
        }
      },
      { $sort: { revenue: -1 } },
      { $limit: 5 }
    ]);

    // Get active systems count
    const store = await Store.findById(storeId);
    const activeSystems = store?.systems?.filter(s => s.status === 'Active').length || 0;

    // Get live activity (recent transactions)
    const liveActivity = await Transaction.find({
      storeId,
      status: 'completed',
      createdAt: { $gte: new Date(now.getTime() - 60 * 60 * 1000) } // Last hour
    })
    .sort({ createdAt: -1 })
    .limit(10)
    .select('amount items.name createdAt systemId')
    .lean();

    const formattedActivity = liveActivity.map(transaction => ({
      type: 'sale',
      message: `Sale completed on ${transaction.systemId || 'System'} - ${transaction.items?.length || 0} items`,
      amount: `R${transaction.amount.toFixed(2)}`,
      timestamp: new Date(transaction.createdAt).toLocaleTimeString()
    }));

    res.status(200).json({
      totalRevenue: current.totalRevenue,
      totalTransactions: current.totalTransactions,
      averageOrder: current.averageOrder,
      revenueChange: parseFloat(revenueChange),
      transactionChange: parseFloat(transactionChange),
      averageOrderChange: parseFloat(averageOrderChange),
      activeSystems,
      revenueData: revenueData.map(item => ({
        time: item.time,
        revenue: item.revenue
      })),
      paymentMethods: paymentMethods.map(method => ({
        name: method.name,
        value: method.count
      })),
      peakHours: peakHours.map(hour => ({
        hour: `${hour.hour}:00`,
        transactions: hour.transactions
      })),
      topProducts,
      liveActivity: formattedActivity,
      timeframe,
      dateRange: {
        start: startDate,
        end: now
      }
    });

  } catch (error) {
    console.error('Analytics error:', error);
    res.status(500).json({ 
      message: 'Failed to get analytics', 
      error: error.message 
    });
  }
};

/**
 * Get real-time metrics for dashboard widgets
 */
exports.getRealTimeMetrics = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // Get today's metrics
    const todayMetrics = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: todayStart }
        }
      },
      {
        $group: {
          _id: null,
          revenue: { $sum: '$amount' },
          transactions: { $sum: 1 },
          lastTransaction: { $max: '$createdAt' }
        }
      }
    ]);

    // Get active systems
    const store = await Store.findById(storeId);
    const activeSystems = store?.systems?.filter(s => s.status === 'Active').length || 0;

    // Get current hour transactions
    const currentHourStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours());
    const currentHourTransactions = await Transaction.countDocuments({
      storeId,
      status: 'completed',
      createdAt: { $gte: currentHourStart }
    });

    const metrics = todayMetrics[0] || { revenue: 0, transactions: 0, lastTransaction: null };

    res.status(200).json({
      todayRevenue: metrics.revenue,
      todayTransactions: metrics.transactions,
      activeSystems,
      currentHourTransactions,
      lastTransactionTime: metrics.lastTransaction,
      timestamp: now
    });

  } catch (error) {
    console.error('Real-time metrics error:', error);
    res.status(500).json({ 
      message: 'Failed to get real-time metrics', 
      error: error.message 
    });
  }
};

/**
 * Get customer analytics
 */
exports.getCustomerAnalytics = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { timeframe = 'week' } = req.query;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    const now = new Date();
    let startDate;
    
    switch (timeframe) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // Get detailed shopping patterns by hour and day
    const shoppingPatterns = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            hour: { $hour: '$createdAt' },
            dayOfWeek: { $dayOfWeek: '$createdAt' },
            date: { $dateToString: { format: "%Y-%m-%d", date: '$createdAt' } }
          },
          transactions: { $sum: 1 },
          averageBasket: { $avg: '$amount' },
          totalItems: { $sum: { $size: '$items' } },
          totalRevenue: { $sum: '$amount' },
          uniqueCustomers: { $addToSet: '$userId' }
        }
      },
      {
        $project: {
          hour: '$_id.hour',
          dayOfWeek: '$_id.dayOfWeek',
          date: '$_id.date',
          transactions: 1,
          averageBasket: 1,
          totalItems: 1,
          totalRevenue: 1,
          uniqueCustomers: { $size: '$uniqueCustomers' },
          itemsPerTransaction: { $divide: ['$totalItems', '$transactions'] }
        }
      },
      { $sort: { date: 1, hour: 1 } }
    ]);

    // Get peak shopping hours
    const peakHours = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: { $hour: '$createdAt' },
          transactions: { $sum: 1 },
          revenue: { $sum: '$amount' }
        }
      },
      { $sort: { transactions: -1 } },
      { $limit: 5 }
    ]);

    // Get basket analysis
    const basketAnalysis = await Transaction.aggregate([
      {
        $match: {
          storeId: require('mongoose').Types.ObjectId(storeId),
          status: 'completed',
          createdAt: { $gte: startDate }
        }
      },
      {
        $project: {
          itemCount: { $size: '$items' },
          amount: 1,
          averageItemPrice: { $divide: ['$amount', { $size: '$items' }] }
        }
      },
      {
        $group: {
          _id: null,
          averageBasketSize: { $avg: '$itemCount' },
          averageBasketValue: { $avg: '$amount' },
          averageItemPrice: { $avg: '$averageItemPrice' },
          smallBaskets: { $sum: { $cond: [{ $lte: ['$itemCount', 3] }, 1, 0] } },
          mediumBaskets: { $sum: { $cond: [{ $and: [{ $gt: ['$itemCount', 3] }, { $lte: ['$itemCount', 10] }] }, 1, 0] } },
          largeBaskets: { $sum: { $cond: [{ $gt: ['$itemCount', 10] }, 1, 0] } },
          totalTransactions: { $sum: 1 }
        }
      }
    ]);

    // Calculate cart abandonment (mock calculation)
    const totalSessions = shoppingPatterns.reduce((sum, p) => sum + p.transactions, 0) * 1.15; // Assume 15% abandonment
    const completedTransactions = shoppingPatterns.reduce((sum, p) => sum + p.transactions, 0);
    const cartAbandonmentRate = ((totalSessions - completedTransactions) / totalSessions * 100).toFixed(1);

    // Calculate customer retention metrics
    const retentionMetrics = {
      newCustomers: Math.floor(completedTransactions * 0.6), // Mock: 60% new customers
      returningCustomers: Math.floor(completedTransactions * 0.4), // Mock: 40% returning
      averageVisitsPerCustomer: 2.3, // Mock data
      customerLifetimeValue: 450 // Mock data in ZAR
    };

    res.status(200).json({
      shoppingPatterns,
      peakHours,
      basketAnalysis: basketAnalysis[0] || {},
      cartAbandonmentRate: parseFloat(cartAbandonmentRate),
      retentionMetrics,
      insights: {
        busiestHour: peakHours[0]?._id || 14,
        averageShoppingDuration: 8.5, // Mock data
        customerSatisfactionScore: 4.2,
        recommendationRate: 87.5
      },
      timeframe
    });

  } catch (error) {
    console.error('Customer analytics error:', error);
    res.status(500).json({
      message: 'Failed to get customer analytics',
      error: error.message
    });
  }
};

/**
 * Get enhanced system analytics with detailed revenue breakdowns
 */
exports.getSystemAnalytics = async (req, res) => {
  try {
    const { storeId } = req.params;
    const { timeframe = 'week', systemId } = req.query;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    const now = new Date();
    let startDate;

    switch (timeframe) {
      case 'day':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // Get store and systems
    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ message: "Store not found" });
    }

    // Build match criteria
    const matchCriteria = {
      storeId: require('mongoose').Types.ObjectId(storeId),
      status: 'completed',
      createdAt: { $gte: startDate }
    };

    if (systemId) {
      matchCriteria.systemId = systemId;
    }

    // Get hourly revenue breakdown by system
    const hourlyRevenue = await Transaction.aggregate([
      { $match: matchCriteria },
      {
        $group: {
          _id: {
            systemId: '$systemId',
            hour: { $hour: '$createdAt' },
            date: { $dateToString: { format: "%Y-%m-%d", date: '$createdAt' } }
          },
          revenue: { $sum: '$amount' },
          transactions: { $sum: 1 },
          averageTransaction: { $avg: '$amount' },
          totalItems: { $sum: { $size: '$items' } }
        }
      },
      {
        $project: {
          systemId: '$_id.systemId',
          hour: '$_id.hour',
          date: '$_id.date',
          revenue: 1,
          transactions: 1,
          averageTransaction: 1,
          totalItems: 1,
          revenuePerHour: '$revenue',
          transactionsPerHour: '$transactions'
        }
      },
      { $sort: { date: 1, hour: 1 } }
    ]);

    // Get daily revenue breakdown by system
    const dailyRevenue = await Transaction.aggregate([
      { $match: matchCriteria },
      {
        $group: {
          _id: {
            systemId: '$systemId',
            date: { $dateToString: { format: "%Y-%m-%d", date: '$createdAt' } }
          },
          revenue: { $sum: '$amount' },
          transactions: { $sum: 1 },
          averageTransaction: { $avg: '$amount' },
          totalItems: { $sum: { $size: '$items' } },
          peakHour: { $max: { $hour: '$createdAt' } },
          firstTransaction: { $min: '$createdAt' },
          lastTransaction: { $max: '$createdAt' }
        }
      },
      {
        $project: {
          systemId: '$_id.systemId',
          date: '$_id.date',
          revenue: 1,
          transactions: 1,
          averageTransaction: 1,
          totalItems: 1,
          peakHour: 1,
          operatingHours: {
            $divide: [
              { $subtract: ['$lastTransaction', '$firstTransaction'] },
              1000 * 60 * 60 // Convert to hours
            ]
          }
        }
      },
      { $sort: { date: 1 } }
    ]);

    // Get system performance metrics
    const systemPerformance = await Transaction.aggregate([
      { $match: matchCriteria },
      {
        $group: {
          _id: '$systemId',
          totalRevenue: { $sum: '$amount' },
          totalTransactions: { $sum: 1 },
          averageTransaction: { $avg: '$amount' },
          totalItems: { $sum: { $size: '$items' } },
          peakDay: { $max: { $dayOfWeek: '$createdAt' } },
          peakHour: { $max: { $hour: '$createdAt' } },
          firstTransaction: { $min: '$createdAt' },
          lastTransaction: { $max: '$createdAt' },
          uniqueDays: { $addToSet: { $dateToString: { format: "%Y-%m-%d", date: '$createdAt' } } }
        }
      },
      {
        $project: {
          systemId: '$_id',
          totalRevenue: 1,
          totalTransactions: 1,
          averageTransaction: 1,
          totalItems: 1,
          peakDay: 1,
          peakHour: 1,
          operatingDays: { $size: '$uniqueDays' },
          revenuePerDay: { $divide: ['$totalRevenue', { $size: '$uniqueDays' }] },
          transactionsPerDay: { $divide: ['$totalTransactions', { $size: '$uniqueDays' }] },
          efficiency: { $divide: ['$totalRevenue', '$totalTransactions'] }
        }
      },
      { $sort: { totalRevenue: -1 } }
    ]);

    // Calculate system rankings and comparisons
    const systemRankings = systemPerformance.map((system, index) => ({
      ...system,
      rank: index + 1,
      performanceScore: calculatePerformanceScore(system)
    }));

    // Get peak performance times
    const peakTimes = await Transaction.aggregate([
      { $match: matchCriteria },
      {
        $group: {
          _id: {
            systemId: '$systemId',
            hour: { $hour: '$createdAt' },
            dayOfWeek: { $dayOfWeek: '$createdAt' }
          },
          revenue: { $sum: '$amount' },
          transactions: { $sum: 1 }
        }
      },
      { $sort: { revenue: -1 } },
      { $limit: 10 }
    ]);

    res.status(200).json({
      hourlyRevenue,
      dailyRevenue,
      systemPerformance: systemRankings,
      peakTimes,
      summary: {
        totalSystems: store.systems.length,
        activeSystems: store.systems.filter(s => s.status === 'Active').length,
        totalRevenue: systemPerformance.reduce((sum, s) => sum + s.totalRevenue, 0),
        totalTransactions: systemPerformance.reduce((sum, s) => sum + s.totalTransactions, 0),
        averageSystemRevenue: systemPerformance.reduce((sum, s) => sum + s.totalRevenue, 0) / systemPerformance.length || 0
      },
      timeframe,
      dateRange: { start: startDate, end: now }
    });

  } catch (error) {
    console.error('System analytics error:', error);
    res.status(500).json({
      message: 'Failed to get system analytics',
      error: error.message
    });
  }
};

// Helper function to calculate performance score
function calculatePerformanceScore(system) {
  const revenueWeight = 0.4;
  const transactionWeight = 0.3;
  const efficiencyWeight = 0.3;

  // Normalize values (this would be better with actual min/max from all systems)
  const revenueScore = Math.min(system.totalRevenue / 10000, 1) * 100;
  const transactionScore = Math.min(system.totalTransactions / 1000, 1) * 100;
  const efficiencyScore = Math.min(system.efficiency / 100, 1) * 100;

  return Math.round(
    (revenueScore * revenueWeight) +
    (transactionScore * transactionWeight) +
    (efficiencyScore * efficiencyWeight)
  );
}
