#!/bin/bash

# Make all setup scripts executable
echo "🔧 Making setup scripts executable..."

chmod +x setup-raspberry-pi.sh
chmod +x setup-thermal-printer.sh  
chmod +x setup-barcode-scanner.sh
chmod +x make-scripts-executable.sh

echo "✅ All setup scripts are now executable!"
echo ""
echo "📋 Available setup scripts:"
echo "- setup-raspberry-pi.sh      : Complete Raspberry Pi setup"
echo "- setup-thermal-printer.sh   : Thermal receipt printer setup"
echo "- setup-barcode-scanner.sh   : Angel Q-A202 barcode scanner setup"
echo ""
echo "📖 Documentation:"
echo "- COMPLETE_DEPLOYMENT_GUIDE.md : Full deployment guide"
echo "- README.md                    : Project overview"
echo ""
echo "🚀 Quick start:"
echo "1. Run: ./setup-raspberry-pi.sh"
echo "2. Copy sys2.py and system_config.json to Pi"
echo "3. Configure hardware with specific setup scripts"
echo "4. Follow COMPLETE_DEPLOYMENT_GUIDE.md for full deployment"
