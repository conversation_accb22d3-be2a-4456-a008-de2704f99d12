import { ReactElement } from 'react';
import { LegendType } from '../..';
import { PieSectorDataItem } from '../../polar/Pie';
import { RechartsRootState } from '../store';
import { ChartData } from '../chartDataSlice';
import { DataKey } from '../../util/types';
import { TooltipType } from '../../component/DefaultTooltipContent';
import type { LegendPayload } from '../../component/DefaultLegendContent';
export type ResolvedPieSettings = {
    name: string | number | undefined;
    nameKey: DataKey<any>;
    data: ChartData | undefined;
    dataKey: DataKey<any> | undefined;
    tooltipType?: TooltipType | undefined;
    legendType: LegendType;
    fill: string;
    cx?: number | string;
    cy?: number | string;
    startAngle?: number;
    endAngle?: number;
    paddingAngle?: number;
    minAngle?: number;
    innerRadius?: number | string;
    outerRadius?: number | string | ((element: any) => number);
    cornerRadius?: number | string;
    presentationProps?: Record<string, string>;
};
export declare const selectDisplayedData: (state: RechartsRootState, pieSettings: ResolvedPieSettings, cells: ReadonlyArray<ReactElement> | undefined) => ChartData | undefined;
export declare const selectPieLegend: (state: RechartsRootState, pieSettings: ResolvedPieSettings, cells: ReadonlyArray<ReactElement> | undefined) => ReadonlyArray<LegendPayload> | undefined;
export declare const selectPieSectors: (state: RechartsRootState, pieSettings: ResolvedPieSettings, cells: ReadonlyArray<ReactElement> | undefined) => Readonly<PieSectorDataItem[]> | undefined;
