#!/bin/bash

# Thermal Receipt Printer Setup Script for Raspberry Pi
# Compatible with most USB thermal receipt printers

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

log "🖨️  Setting up thermal receipt printer..."

# Check if CUPS is installed
if ! command -v lpadmin &> /dev/null; then
    log "Installing CUPS printing system..."
    sudo apt update
    sudo apt install -y cups cups-client cups-bsd
    sudo usermod -a -G lpadmin pi
fi

# Start CUPS service
log "Starting CUPS service..."
sudo systemctl enable cups
sudo systemctl start cups

# Detect USB printers
log "🔍 Detecting USB printers..."
USB_DEVICES=$(lsusb | grep -i -E "(printer|pos|thermal|receipt)" || true)

if [ -z "$USB_DEVICES" ]; then
    warn "No thermal printers detected via USB"
    info "Please ensure your thermal printer is:"
    info "1. Connected via USB"
    info "2. Powered on"
    info "3. Compatible with Linux"
    echo ""
    info "Common thermal printer vendors: Epson, Star, Citizen, Bixolon"
else
    log "Found potential thermal printers:"
    echo "$USB_DEVICES"
fi

# List available printer URIs
log "📋 Available printer URIs:"
lpinfo -v 2>/dev/null | grep -E "(usb|serial)" || warn "No USB/Serial printers detected"

echo ""
info "🔧 Manual printer setup options:"
echo ""

# Option 1: Auto-detect and setup
log "Option 1: Auto-detect thermal printer"
PRINTER_URI=$(lpinfo -v 2>/dev/null | grep "usb://" | head -1 | awk '{print $2}' || true)

if [ -n "$PRINTER_URI" ]; then
    info "Found printer URI: $PRINTER_URI"
    
    read -p "Setup printer with URI $PRINTER_URI? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log "Setting up thermal printer..."
        
        # Remove existing printer if it exists
        sudo lpadmin -x ReceiptPrinter 2>/dev/null || true
        
        # Add thermal printer
        sudo lpadmin -p ReceiptPrinter -E -v "$PRINTER_URI" -m raw
        
        # Set as default printer
        sudo lpoptions -d ReceiptPrinter
        
        # Configure printer options for thermal printing
        sudo lpadmin -p ReceiptPrinter -o printer-is-shared=false
        sudo lpadmin -p ReceiptPrinter -o media=Custom.80x200mm
        
        log "✅ Thermal printer 'ReceiptPrinter' configured successfully!"
        
        # Test print
        read -p "Test print receipt? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            log "Sending test print..."
            cat > /tmp/test_receipt.txt << 'EOF'
================================
         TEST RECEIPT
================================

Date: $(date)
System: Raspberry Pi Self-Checkout

Items:
--------------------------------
Test Item 1          R10.00
Test Item 2          R25.50
--------------------------------
TOTAL:               R35.50
================================

Thank you for testing!


EOF
            lp -d ReceiptPrinter /tmp/test_receipt.txt
            log "✅ Test receipt sent to printer!"
            rm /tmp/test_receipt.txt
        fi
    fi
else
    warn "No USB printers auto-detected"
fi

echo ""
log "Option 2: Manual printer setup"
info "If auto-detection failed, you can manually configure:"
echo ""
info "1. Find your printer URI:"
info "   lpinfo -v"
echo ""
info "2. Add printer manually:"
info "   sudo lpadmin -p ReceiptPrinter -E -v [YOUR_PRINTER_URI] -m raw"
echo ""
info "3. Set as default:"
info "   sudo lpoptions -d ReceiptPrinter"
echo ""

log "Option 3: Common thermal printer models"
echo ""
info "Epson TM series:"
info "   sudo lpadmin -p ReceiptPrinter -E -v usb://EPSON/TM-T20 -m raw"
echo ""
info "Star TSP series:"
info "   sudo lpadmin -p ReceiptPrinter -E -v usb://Star/TSP143 -m raw"
echo ""
info "Generic thermal printer:"
info "   sudo lpadmin -p ReceiptPrinter -E -v usb://Unknown/Printer -m raw"
echo ""

# Create printer test script
log "📝 Creating printer test script..."
cat > test_thermal_printer.sh << 'EOF'
#!/bin/bash

echo "🖨️  Testing thermal printer..."

# Check if printer exists
if ! lpstat -p ReceiptPrinter &>/dev/null; then
    echo "❌ ReceiptPrinter not found. Please run setup first."
    exit 1
fi

# Create test receipt
cat > /tmp/thermal_test.txt << 'RECEIPT'
================================
      THERMAL PRINTER TEST
================================

Date: $(date '+%Y-%m-%d %H:%M:%S')
System: Self-Checkout Pi

Test Items:
--------------------------------
Coca Cola 330ml       R15.00
Bread Loaf            R12.50
Milk 1L               R18.99
--------------------------------
Subtotal:             R46.49
VAT (15%):            R6.97
--------------------------------
TOTAL:                R53.46
================================

Thank you for shopping!
Please keep your receipt.


RECEIPT

# Print the test receipt
echo "Sending test receipt to printer..."
lp -d ReceiptPrinter /tmp/thermal_test.txt

if [ $? -eq 0 ]; then
    echo "✅ Test receipt sent successfully!"
    echo "Check your thermal printer for output."
else
    echo "❌ Failed to send test receipt."
    echo "Check printer connection and configuration."
fi

# Cleanup
rm /tmp/thermal_test.txt
EOF

chmod +x test_thermal_printer.sh

# Create printer status script
cat > check_printer_status.sh << 'EOF'
#!/bin/bash

echo "🖨️  Printer Status Check"
echo "======================="

# Check if CUPS is running
if systemctl is-active --quiet cups; then
    echo "✅ CUPS service: Running"
else
    echo "❌ CUPS service: Not running"
    echo "   Start with: sudo systemctl start cups"
fi

# Check configured printers
echo ""
echo "📋 Configured Printers:"
lpstat -p 2>/dev/null || echo "No printers configured"

# Check default printer
echo ""
echo "🎯 Default Printer:"
lpstat -d 2>/dev/null || echo "No default printer set"

# Check printer queue
echo ""
echo "📄 Print Queue:"
lpstat -o 2>/dev/null || echo "Print queue is empty"

# Check USB devices
echo ""
echo "🔌 USB Devices:"
lsusb | grep -i -E "(printer|pos|thermal|receipt)" || echo "No thermal printers detected"

# Check printer URIs
echo ""
echo "🔗 Available Printer URIs:"
lpinfo -v 2>/dev/null | grep -E "(usb|serial)" || echo "No USB/Serial printers found"
EOF

chmod +x check_printer_status.sh

log "✅ Thermal printer setup completed!"
echo ""
info "📋 Created scripts:"
info "- test_thermal_printer.sh  : Test your thermal printer"
info "- check_printer_status.sh  : Check printer status and configuration"
echo ""
info "🔧 Useful commands:"
info "- Check printer status: lpstat -p ReceiptPrinter"
info "- Test print: echo 'Hello World' | lp -d ReceiptPrinter"
info "- View print queue: lpstat -o"
info "- Cancel all jobs: cancel -a"
echo ""
info "🚨 Troubleshooting:"
info "- If printer not detected: Check USB connection and power"
info "- If printing fails: Check printer driver and paper"
info "- If garbled output: Verify printer model and settings"
info "- View CUPS logs: sudo tail -f /var/log/cups/error_log"
echo ""
warn "⚠️  Note: Some thermal printers may require specific drivers."
warn "   Check manufacturer documentation for Linux compatibility."
