const mongoose = require("mongoose");

const systemSchema = new mongoose.Schema({
  systemId: {
    type: String,
    required: true,
    unique: true
  },
  location: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: false
  },
  status: {
    type: String,
    enum: ['Active', 'Inactive', 'Maintenance'],
    default: 'Active'
  },
  registeredAt: {
    type: Date,
    default: Date.now
  },
  lastSeen: {
    type: Date,
    required: false
  }
});

const payfastSchema = new mongoose.Schema({
  merchantId: {
    type: String,
    required: false
  },
  merchantKey: {
    type: String,
    required: false
  },
  passphrase: {
    type: String,
    required: false
  },
  sandbox: {
    type: Boolean,
    default: true
  },
  posDeviceId: {
    type: String,
    required: false
  },
  isActive: {
    type: Boolean,
    default: false
  },
  setupComplete: {
    type: Boolean,
    default: false
  }
});

const receiptTemplateSchema = new mongoose.Schema({
  templateName: {
    type: String,
    default: 'Default Template'
  },
  isActive: {
    type: Boolean,
    default: true
  },
  header: {
    storeName: {
      show: { type: Boolean, default: true },
      text: { type: String, default: '' },
      fontSize: { type: Number, default: 18 },
      alignment: { type: String, enum: ['left', 'center', 'right'], default: 'center' },
      bold: { type: Boolean, default: true }
    },
    storeAddress: {
      show: { type: Boolean, default: true },
      text: { type: String, default: '' },
      fontSize: { type: Number, default: 12 },
      alignment: { type: String, enum: ['left', 'center', 'right'], default: 'center' }
    },
    storeContact: {
      show: { type: Boolean, default: true },
      phone: { type: String, default: '' },
      email: { type: String, default: '' },
      website: { type: String, default: '' },
      fontSize: { type: Number, default: 10 },
      alignment: { type: String, enum: ['left', 'center', 'right'], default: 'center' }
    },
    logo: {
      show: { type: Boolean, default: false },
      path: { type: String, default: '' },
      width: { type: Number, default: 100 },
      height: { type: Number, default: 50 },
      alignment: { type: String, enum: ['left', 'center', 'right'], default: 'center' }
    },
    customMessage: {
      show: { type: Boolean, default: false },
      text: { type: String, default: '' },
      fontSize: { type: Number, default: 12 },
      alignment: { type: String, enum: ['left', 'center', 'right'], default: 'center' }
    }
  },
  body: {
    showDateTime: { type: Boolean, default: true },
    dateTimeFormat: { type: String, default: 'YYYY-MM-DD HH:mm:ss' },
    showTransactionId: { type: Boolean, default: true },
    showCashier: { type: Boolean, default: false },
    cashierName: { type: String, default: 'Self-Checkout' },
    itemsTable: {
      showHeaders: { type: Boolean, default: true },
      showQuantity: { type: Boolean, default: true },
      showUnitPrice: { type: Boolean, default: true },
      showTotalPrice: { type: Boolean, default: true },
      showBarcode: { type: Boolean, default: false },
      fontSize: { type: Number, default: 10 }
    },
    pricing: {
      showSubtotal: { type: Boolean, default: true },
      showTax: { type: Boolean, default: false },
      taxRate: { type: Number, default: 15 },
      showDiscount: { type: Boolean, default: false },
      showTotal: { type: Boolean, default: true },
      currency: { type: String, default: 'R' },
      fontSize: { type: Number, default: 12 }
    }
  },
  footer: {
    thankYouMessage: {
      show: { type: Boolean, default: true },
      text: { type: String, default: 'Thank you for shopping with us!' },
      fontSize: { type: Number, default: 12 },
      alignment: { type: String, enum: ['left', 'center', 'right'], default: 'center' }
    },
    returnPolicy: {
      show: { type: Boolean, default: false },
      text: { type: String, default: '' },
      fontSize: { type: Number, default: 8 },
      alignment: { type: String, enum: ['left', 'center', 'right'], default: 'center' }
    },
    socialMedia: {
      show: { type: Boolean, default: false },
      facebook: { type: String, default: '' },
      instagram: { type: String, default: '' },
      twitter: { type: String, default: '' },
      fontSize: { type: Number, default: 8 },
      alignment: { type: String, enum: ['left', 'center', 'right'], default: 'center' }
    },
    customFooter: {
      show: { type: Boolean, default: false },
      text: { type: String, default: '' },
      fontSize: { type: Number, default: 10 },
      alignment: { type: String, enum: ['left', 'center', 'right'], default: 'center' }
    },
    qrCode: {
      show: { type: Boolean, default: false },
      type: { type: String, enum: ['feedback', 'website', 'loyalty', 'custom'], default: 'feedback' },
      url: { type: String, default: '' },
      size: { type: Number, default: 100 },
      alignment: { type: String, enum: ['left', 'center', 'right'], default: 'center' },
      label: { type: String, default: 'Scan for feedback' }
    },
    promotionalMessage: {
      show: { type: Boolean, default: false },
      text: { type: String, default: '' },
      fontSize: { type: Number, default: 10 },
      alignment: { type: String, enum: ['left', 'center', 'right'], default: 'center' },
      backgroundColor: { type: String, default: '#f0f0f0' },
      textColor: { type: String, default: '#000000' }
    }
  },
  advanced: {
    multiLanguage: {
      enabled: { type: Boolean, default: false },
      primaryLanguage: { type: String, default: 'en' },
      secondaryLanguage: { type: String, default: '' },
      translations: {
        type: Map,
        of: String,
        default: new Map()
      }
    },
    loyaltyProgram: {
      enabled: { type: Boolean, default: false },
      programName: { type: String, default: '' },
      pointsEarned: { type: Boolean, default: false },
      membershipInfo: { type: Boolean, default: false },
      qrCodeIntegration: { type: Boolean, default: false }
    },
    dynamicContent: {
      enabled: { type: Boolean, default: false },
      weatherInfo: { type: Boolean, default: false },
      dailySpecials: { type: Boolean, default: false },
      personalizedOffers: { type: Boolean, default: false }
    }
  },
  layout: {
    paperWidth: { type: Number, default: 80 }, // mm
    margin: { type: Number, default: 5 }, // mm
    lineSpacing: { type: Number, default: 1.2 },
    sectionSpacing: { type: Number, default: 10 } // pixels
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

const storeSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
  },
  email: {
    type: String,
    required: true,
    unique: true
  },
  password: {
    type: String,
    required: true
  },
  systems: [systemSchema],
  flyer: {
    type: String,
  },
  payfast: {
    type: payfastSchema,
    default: () => ({})
  },
  receiptTemplate: {
    type: receiptTemplateSchema,
    default: () => ({})
  },
  createdAt: {
    type: Date,
    default: Date.now
  }
});

module.exports = mongoose.model("Store", storeSchema);