const express = require("express");
const { authenticate, authorize } = require("../middleware/authMiddleware");
const {
  getStoreAnalytics,
  getRealTimeMetrics,
  getCustomerAnalytics,
  getSystemAnalytics
} = require("../controllers/analyticsController");

const router = express.Router();

// Store analytics
router.get("/store/:storeId", authenticate, authorize(["admin", "store"]), getStoreAnalytics);

// Real-time metrics
router.get("/store/:storeId/realtime", authenticate, authorize(["admin", "store"]), getRealTimeMetrics);

// Customer analytics
router.get("/store/:storeId/customers", authenticate, authorize(["admin", "store"]), getCustomerAnalytics);

// Enhanced system analytics
router.get("/store/:storeId/systems", authenticate, authorize(["admin", "store"]), getSystemAnalytics);

module.exports = router;
