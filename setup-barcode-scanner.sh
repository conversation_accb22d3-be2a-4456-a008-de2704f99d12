#!/bin/bash

# Angel Q-A202 Barcode Scanner Setup Script for Raspberry Pi
# Compatible with USB HID barcode scanners

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

log "📊 Setting up Angel Q-A202 Barcode Scanner..."

# Install required packages
log "📦 Installing required packages..."
sudo apt update
sudo apt install -y python3-evdev udev

# Check for connected USB devices
log "🔍 Detecting USB barcode scanners..."
USB_DEVICES=$(lsusb | grep -i -E "(scanner|barcode|angel|hid)" || true)

if [ -z "$USB_DEVICES" ]; then
    warn "No barcode scanners detected via USB"
    info "Please ensure your Angel Q-A202 scanner is:"
    info "1. Connected via USB"
    info "2. Powered on (if external power required)"
    info "3. In HID mode (keyboard emulation)"
else
    log "Found potential barcode scanners:"
    echo "$USB_DEVICES"
fi

# List input devices
log "📋 Available input devices:"
python3 -c "
import evdev
devices = [evdev.InputDevice(path) for path in evdev.list_devices()]
for device in devices:
    print(f'Device: {device.path} - {device.name}')
    if any(keyword in device.name.lower() for keyword in ['scanner', 'barcode', 'angel', 'hid']):
        print(f'  *** Potential barcode scanner: {device.name} ***')
" 2>/dev/null || warn "Could not list input devices"

# Create udev rule for barcode scanner access
log "⚙️  Creating udev rule for barcode scanner access..."
sudo tee /etc/udev/rules.d/99-barcode-scanner.rules > /dev/null << 'EOF'
# Angel Q-A202 and generic USB barcode scanners
# Allow pi user to access barcode scanner devices
SUBSYSTEM=="input", GROUP="input", MODE="0664"
SUBSYSTEM=="usb", ATTRS{idVendor}=="*", ATTRS{idProduct}=="*", ATTRS{product}=="*Scanner*", GROUP="plugdev", MODE="0664"
SUBSYSTEM=="usb", ATTRS{idVendor}=="*", ATTRS{idProduct}=="*", ATTRS{product}=="*Barcode*", GROUP="plugdev", MODE="0664"
SUBSYSTEM=="usb", ATTRS{idVendor}=="*", ATTRS{idProduct}=="*", ATTRS{product}=="*Angel*", GROUP="plugdev", MODE="0664"

# Generic HID devices that might be barcode scanners
KERNEL=="event*", SUBSYSTEM=="input", ATTRS{name}=="*Scanner*", GROUP="input", MODE="0664"
KERNEL=="event*", SUBSYSTEM=="input", ATTRS{name}=="*Barcode*", GROUP="input", MODE="0664"
KERNEL=="event*", SUBSYSTEM=="input", ATTRS{name}=="*Angel*", GROUP="input", MODE="0664"
EOF

# Add pi user to input group
log "👤 Adding pi user to input group..."
sudo usermod -a -G input pi
sudo usermod -a -G plugdev pi

# Reload udev rules
log "🔄 Reloading udev rules..."
sudo udevadm control --reload-rules
sudo udevadm trigger

# Create barcode scanner test script
log "📝 Creating barcode scanner test script..."
cat > test_barcode_scanner.py << 'EOF'
#!/usr/bin/env python3

import evdev
import time
import sys
from evdev import InputDevice, categorize, ecodes

def find_barcode_scanner():
    """Find barcode scanner device."""
    devices = [evdev.InputDevice(path) for path in evdev.list_devices()]
    
    print("Available input devices:")
    for device in devices:
        print(f"  {device.path}: {device.name}")
        
        # Check if this might be a barcode scanner
        if any(keyword in device.name.lower() for keyword in ['scanner', 'barcode', 'angel']):
            print(f"  *** Potential barcode scanner found: {device.name} ***")
            return device
    
    # If no obvious scanner found, let user choose
    print("\nNo obvious barcode scanner found.")
    print("Please check if your Angel Q-A202 is:")
    print("1. Connected via USB")
    print("2. Configured in HID/Keyboard mode")
    print("3. Powered on")
    
    return None

def key_to_char(key):
    """Convert key code to character."""
    key_map = {
        'KEY_0': '0', 'KEY_1': '1', 'KEY_2': '2', 'KEY_3': '3', 'KEY_4': '4',
        'KEY_5': '5', 'KEY_6': '6', 'KEY_7': '7', 'KEY_8': '8', 'KEY_9': '9',
        'KEY_A': 'A', 'KEY_B': 'B', 'KEY_C': 'C', 'KEY_D': 'D', 'KEY_E': 'E',
        'KEY_F': 'F', 'KEY_G': 'G', 'KEY_H': 'H', 'KEY_I': 'I', 'KEY_J': 'J',
        'KEY_K': 'K', 'KEY_L': 'L', 'KEY_M': 'M', 'KEY_N': 'N', 'KEY_O': 'O',
        'KEY_P': 'P', 'KEY_Q': 'Q', 'KEY_R': 'R', 'KEY_S': 'S', 'KEY_T': 'T',
        'KEY_U': 'U', 'KEY_V': 'V', 'KEY_W': 'W', 'KEY_X': 'X', 'KEY_Y': 'Y',
        'KEY_Z': 'Z', 'KEY_MINUS': '-', 'KEY_EQUAL': '='
    }
    return key_map.get(key, '')

def test_scanner(device):
    """Test barcode scanner input."""
    print(f"\n🔍 Testing barcode scanner: {device.name}")
    print("Scan a barcode now (or press Ctrl+C to exit)...")
    
    barcode_buffer = ""
    
    try:
        for event in device.read_loop():
            if event.type == ecodes.EV_KEY:
                key_event = categorize(event)
                if key_event.keystate == key_event.key_down:
                    key = key_event.keycode
                    
                    if key == 'KEY_ENTER':
                        if barcode_buffer.strip():
                            print(f"✅ Barcode detected: {barcode_buffer}")
                            barcode_buffer = ""
                        else:
                            print("Empty barcode received")
                    else:
                        char = key_to_char(key)
                        if char:
                            barcode_buffer += char
                            print(f"Reading: {barcode_buffer}", end='\r')
                            
    except KeyboardInterrupt:
        print("\n\n🛑 Test stopped by user")
    except PermissionError:
        print("❌ Permission denied. Make sure you're in the 'input' group and udev rules are applied.")
        print("Try: sudo usermod -a -G input $USER")
        print("Then log out and log back in.")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    print("📊 Angel Q-A202 Barcode Scanner Test")
    print("=" * 40)
    
    scanner = find_barcode_scanner()
    
    if scanner:
        try:
            test_scanner(scanner)
        except Exception as e:
            print(f"❌ Failed to test scanner: {e}")
    else:
        print("\n❌ No barcode scanner found.")
        print("\nTroubleshooting:")
        print("1. Check USB connection")
        print("2. Ensure scanner is in HID mode")
        print("3. Try unplugging and reconnecting")
        print("4. Check if device appears in 'lsusb' output")

if __name__ == "__main__":
    main()
EOF

chmod +x test_barcode_scanner.py

# Create scanner configuration script
cat > configure_scanner.py << 'EOF'
#!/usr/bin/env python3

import evdev
import json
import os

def detect_and_save_scanner():
    """Detect barcode scanner and save configuration."""
    devices = [evdev.InputDevice(path) for path in evdev.list_devices()]
    
    scanner_config = {
        "scanner_found": False,
        "scanner_path": None,
        "scanner_name": None,
        "all_devices": []
    }
    
    for device in devices:
        device_info = {
            "path": device.path,
            "name": device.name,
            "is_scanner": False
        }
        
        # Check if this might be a barcode scanner
        if any(keyword in device.name.lower() for keyword in ['scanner', 'barcode', 'angel']):
            device_info["is_scanner"] = True
            scanner_config["scanner_found"] = True
            scanner_config["scanner_path"] = device.path
            scanner_config["scanner_name"] = device.name
            print(f"✅ Found barcode scanner: {device.name} at {device.path}")
        
        scanner_config["all_devices"].append(device_info)
    
    # Save configuration
    config_path = "/home/<USER>/selfcheckout/scanner_config.json"
    os.makedirs(os.path.dirname(config_path), exist_ok=True)
    
    with open(config_path, 'w') as f:
        json.dump(scanner_config, f, indent=2)
    
    print(f"📝 Scanner configuration saved to: {config_path}")
    
    if not scanner_config["scanner_found"]:
        print("⚠️  No barcode scanner detected.")
        print("Available devices:")
        for device in scanner_config["all_devices"]:
            print(f"  - {device['path']}: {device['name']}")

if __name__ == "__main__":
    detect_and_save_scanner()
EOF

chmod +x configure_scanner.py

log "✅ Barcode scanner setup completed!"
echo ""
info "📋 Created scripts:"
info "- test_barcode_scanner.py    : Test your Angel Q-A202 scanner"
info "- configure_scanner.py       : Detect and configure scanner"
echo ""
info "🔧 Next steps:"
info "1. Reconnect your Angel Q-A202 scanner"
info "2. Run: python3 configure_scanner.py"
info "3. Test: python3 test_barcode_scanner.py"
info "4. If issues, log out and log back in (for group permissions)"
echo ""
info "📊 Angel Q-A202 Configuration:"
info "- Ensure scanner is in HID/Keyboard emulation mode"
info "- Check scanner manual for mode switching instructions"
info "- Some scanners require specific key combinations to switch modes"
echo ""
info "🚨 Troubleshooting:"
info "- Permission denied: sudo usermod -a -G input pi && logout"
info "- Scanner not detected: Check USB connection and power"
info "- Wrong mode: Configure scanner to HID/Keyboard mode"
info "- Multiple devices: Use configure_scanner.py to identify correct device"
echo ""
warn "⚠️  Note: You may need to log out and log back in for group permissions to take effect."
