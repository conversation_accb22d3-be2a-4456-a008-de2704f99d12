# Complete Self-Checkout System Deployment Guide

## 🎯 System Overview

This self-checkout system consists of three main components designed for retail environments:

- **Backend API** (Node.js/Express) - Handles authentication, product management, payments
- **Web Frontend** (React) - Admin dashboard for store management  
- **Desktop App** (`sys2.py`) - Python application for Raspberry Pi with hardware integration

## 🛠️ Hardware Requirements

### Raspberry Pi Setup
- **Raspberry Pi 4 Model B** (4GB+ RAM recommended)
- **Raspberry Pi 7" Touchscreen Display** (Official)
- **Raspberry Pi Camera Module 3 NOIR** (Standard 76° lens)
- **Angel Q-A202 Barcode Scanner** (USB HID mode)
- **Thermal Receipt Printer** (USB compatible)
- **PayFast POS Card Terminal** (USB/Bluetooth)
- **MicroSD Card** (32GB+ Class 10)
- **Power Supply** (Official Pi 4 PSU recommended)

### Server Requirements
- **CPU**: 2+ cores
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 20GB+ available space
- **OS**: Ubuntu 20.04+ or similar Linux distribution
- **Network**: Stable internet connection

## 📋 Pre-Deployment Checklist

### Backend Server
- [ ] Ubuntu/Linux server with sudo access
- [ ] Node.js 18+ installed
- [ ] MongoDB 5.0+ installed or MongoDB Atlas account
- [ ] Domain name or static IP address
- [ ] SSL certificate (Let's Encrypt recommended)
- [ ] Firewall configured (ports 80, 443, 5000)

### Raspberry Pi
- [ ] Raspberry Pi 4B with fresh Raspberry Pi OS installation
- [ ] All hardware components connected
- [ ] Network connectivity (WiFi or Ethernet)
- [ ] SSH enabled for remote access

### PayFast Account
- [ ] PayFast merchant account
- [ ] POS device credentials
- [ ] Sandbox/Production configuration

## 🚀 Step 1: Backend Server Setup

### 1.1 Clone Repository
```bash
# Clone the repository
git clone <your-repo-url>
cd checkout

# Navigate to backend
cd backend
```

### 1.2 Install Dependencies
```bash
# Install Node.js (if not installed)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install backend dependencies
npm install

# Install PM2 for production
sudo npm install -g pm2
```

### 1.3 Configure Environment
```bash
# Copy environment template
cp .env.example .env

# Edit configuration
nano .env
```

**Required Environment Variables:**
```env
# Database
MONGODB_URI=mongodb://localhost:27017/selfcheckout
# OR for MongoDB Atlas:
# MONGODB_URI=mongodb+srv://username:<EMAIL>/selfcheckout

# Security
JWT_SECRET=your_super_secure_jwt_secret_here_change_this

# Server
PORT=5000
NODE_ENV=production
BASE_URL=https://your-domain.com

# File Upload
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# PayFast
PAYFAST_SANDBOX=false  # Set to true for testing
```

### 1.4 Initialize Database
```bash
# Seed the database with initial data
npm run seed
```

### 1.5 Start Backend Service
```bash
# Test the server
npm start

# For production, use PM2
pm2 start server.js --name selfcheckout-api
pm2 startup
pm2 save
```

### 1.6 Configure Reverse Proxy (Nginx)
```bash
# Install Nginx
sudo apt install nginx

# Create site configuration
sudo nano /etc/nginx/sites-available/selfcheckout
```

**Nginx Configuration:**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/selfcheckout /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx

# Install SSL certificate (Let's Encrypt)
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## 🌐 Step 2: Frontend Deployment

### 2.1 Build Frontend
```bash
# Navigate to frontend
cd ../ui

# Install dependencies
npm install

# Build for production
npm run build
```

### 2.2 Deploy Frontend
```bash
# Copy build files to web server
sudo cp -r dist/* /var/www/html/

# Or serve with Nginx
sudo mkdir -p /var/www/selfcheckout
sudo cp -r dist/* /var/www/selfcheckout/
```

**Update Nginx for Frontend:**
```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    # SSL configuration (added by certbot)
    
    # Serve frontend
    location / {
        root /var/www/selfcheckout;
        try_files $uri $uri/ /index.html;
    }
    
    # API proxy
    location /api/ {
        proxy_pass http://localhost:5000/api/;
        # ... proxy headers as above
    }
}
```

## 🍓 Step 3: Raspberry Pi Setup

### 3.1 Prepare Raspberry Pi
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Download setup script
wget https://raw.githubusercontent.com/your-repo/setup-raspberry-pi.sh
chmod +x setup-raspberry-pi.sh

# Run setup script
./setup-raspberry-pi.sh
```

### 3.2 Copy Application Files
```bash
# Copy sys2.py and config to Pi
scp sys2.py pi@<pi-ip-address>:/home/<USER>/selfcheckout/
scp system_config.json pi@<pi-ip-address>:/home/<USER>/selfcheckout/
```

### 3.3 Configure System
```bash
# SSH into Raspberry Pi
ssh pi@<pi-ip-address>

# Edit system configuration
cd /home/<USER>/selfcheckout
nano system_config.json
```

**System Configuration:**
```json
{
    "system_id": "STORE1-TROLLEY-001",
    "store_name": "Your Store Name",
    "store_id": "your_mongodb_store_id",
    "backend_url": "https://your-domain.com/api"
}
```

### 3.4 Setup Hardware Components

#### Camera Module
```bash
# Test camera
raspistill -o test.jpg
python3 -c "import cv2; cap = cv2.VideoCapture(0); print('Camera OK' if cap.isOpened() else 'Camera Error')"
```

#### Thermal Printer
```bash
# Run printer setup
./setup-thermal-printer.sh

# Test printer
./test_thermal_printer.sh
```

#### Barcode Scanner
```bash
# Run scanner setup
./setup-barcode-scanner.sh

# Test scanner
python3 test_barcode_scanner.py
```

### 3.5 Start Self-Checkout System
```bash
# Test run
./start_selfcheckout.sh

# Enable auto-start
sudo systemctl start selfcheckout.service
sudo systemctl status selfcheckout.service
```

## 💳 Step 4: PayFast Integration

### 4.1 Configure PayFast Account
1. Log into PayFast merchant dashboard
2. Navigate to **Settings > Integration**
3. Note down:
   - Merchant ID
   - Merchant Key
   - Passphrase
   - POS Device ID

### 4.2 Configure Store PayFast Settings
1. Access web frontend: `https://your-domain.com`
2. Login as store admin
3. Navigate to **Settings > Payment**
4. Enter PayFast credentials:
   - Merchant ID
   - Merchant Key  
   - Passphrase
   - POS Device ID
   - Sandbox Mode (for testing)

### 4.3 Test PayFast Integration
```bash
# Test PayFast connection
cd backend
node test-payfast-integration.js
```

## 🔧 Step 5: System Configuration

### 5.1 Create Store and Products
1. Access web frontend
2. Login as admin
3. Create store profile
4. Upload product catalog
5. Configure receipt templates

### 5.2 Register Self-Checkout Systems
1. Navigate to **Systems** tab
2. Add new system with:
   - System ID (from Raspberry Pi config)
   - Store assignment
   - Hardware configuration

### 5.3 Test Complete Workflow
1. Start self-checkout on Raspberry Pi
2. Scan test products
3. Process test payment
4. Verify receipt printing
5. Check transaction in admin dashboard

## 📊 Step 6: Monitoring and Maintenance

### 6.1 System Monitoring
```bash
# Backend logs
pm2 logs selfcheckout-api

# Raspberry Pi logs
journalctl -u selfcheckout.service -f

# System status
./check_printer_status.sh
python3 configure_scanner.py
```

### 6.2 Backup Configuration
```bash
# Backup database
mongodump --db selfcheckout --out backup/

# Backup Pi configuration
tar -czf pi-backup.tar.gz /home/<USER>/selfcheckout/
```

## 🚨 Troubleshooting

### Common Issues

**Backend Connection Issues:**
- Check firewall settings
- Verify MongoDB connection
- Check SSL certificate validity

**Raspberry Pi Issues:**
- Camera not detected: Enable camera interface
- Scanner not working: Check USB connection and HID mode
- Printer not printing: Verify CUPS configuration
- Touch screen not responsive: Check display drivers

**PayFast Issues:**
- Invalid credentials: Verify merchant details
- Sandbox mode: Ensure correct environment setting
- POS device offline: Check device connection

### Support Resources
- Backend API docs: `https://your-domain.com/api-docs`
- System logs: `journalctl -u selfcheckout.service`
- Hardware status: Run diagnostic scripts
- PayFast support: Contact PayFast technical support

## ✅ Deployment Verification

### Final Checklist
- [ ] Backend API responding at `https://your-domain.com/api`
- [ ] Frontend accessible at `https://your-domain.com`
- [ ] Raspberry Pi auto-starts self-checkout system
- [ ] Camera captures and processes barcodes
- [ ] USB barcode scanner functional
- [ ] Thermal printer produces receipts
- [ ] PayFast processes test transactions
- [ ] Admin dashboard shows system status
- [ ] All logs are clean and error-free

## 🔐 Security Considerations

### Backend Security
- Use strong JWT secrets (32+ characters)
- Enable HTTPS with valid SSL certificates
- Configure firewall to allow only necessary ports
- Regular security updates for Node.js and dependencies
- Use environment variables for sensitive data
- Enable MongoDB authentication in production

### Raspberry Pi Security
- Change default passwords
- Enable SSH key authentication
- Disable unnecessary services
- Regular OS updates
- Physical security for devices
- Network segmentation for POS devices

### PayFast Security
- Use production credentials only in production
- Secure storage of merchant keys
- Regular credential rotation
- Monitor transaction logs for anomalies

## 📈 Performance Optimization

### Backend Optimization
```bash
# Enable MongoDB indexing
mongo selfcheckout --eval "
db.products.createIndex({barcode: 1});
db.transactions.createIndex({storeId: 1, createdAt: -1});
db.systems.createIndex({storeId: 1, systemId: 1});
"

# Configure PM2 cluster mode
pm2 start server.js --name selfcheckout-api -i max
```

### Raspberry Pi Optimization
```bash
# Optimize GPU memory split
echo "gpu_mem=128" | sudo tee -a /boot/config.txt

# Disable unnecessary services
sudo systemctl disable bluetooth
sudo systemctl disable wifi-powersave

# Optimize camera settings in sys2.py
# Already implemented in the modified version
```

## 🔄 Backup and Recovery

### Automated Backup Script
```bash
#!/bin/bash
# save as: backup-system.sh

BACKUP_DIR="/backup/selfcheckout/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# Backup MongoDB
mongodump --db selfcheckout --out "$BACKUP_DIR/database/"

# Backup uploads
cp -r backend/uploads "$BACKUP_DIR/"

# Backup configuration
cp backend/.env "$BACKUP_DIR/"
cp system_config.json "$BACKUP_DIR/"

# Create archive
tar -czf "$BACKUP_DIR.tar.gz" "$BACKUP_DIR"
rm -rf "$BACKUP_DIR"

echo "Backup completed: $BACKUP_DIR.tar.gz"
```

### Recovery Procedures
```bash
# Restore database
mongorestore --db selfcheckout backup/database/selfcheckout/

# Restore uploads
cp -r backup/uploads backend/

# Restart services
pm2 restart selfcheckout-api
sudo systemctl restart selfcheckout.service
```

## 📱 Mobile App Integration (Future)

The system is designed to support future mobile app integration:

- QR code generation for mobile payments
- Customer mobile app for self-scanning
- Store staff mobile app for inventory management
- Push notifications for system alerts

## 🌍 Multi-Store Deployment

For multiple store locations:

### Centralized Backend
- Single backend server for all stores
- Store-specific configurations
- Centralized reporting and analytics
- Shared product database with store-specific pricing

### Store-Specific Configuration
```json
{
  "store_id": "store_specific_id",
  "store_name": "Store Location Name",
  "backend_url": "https://central-api.yourcompany.com/api",
  "local_settings": {
    "currency": "ZAR",
    "tax_rate": 0.15,
    "receipt_footer": "Thank you for shopping at Store Name!"
  }
}
```

## 📊 Analytics and Reporting

### Key Metrics Tracked
- Transaction volume and value
- Product scan accuracy
- System uptime and performance
- Payment method preferences
- Peak usage times
- Error rates and types

### Dashboard Features
- Real-time system status
- Sales analytics
- Inventory tracking
- Performance metrics
- Alert management

## 🛠️ Maintenance Schedule

### Daily
- [ ] Check system status dashboard
- [ ] Verify all Pi devices are online
- [ ] Monitor transaction logs for errors

### Weekly
- [ ] Review system performance metrics
- [ ] Check printer paper and supplies
- [ ] Test backup and recovery procedures
- [ ] Update product database if needed

### Monthly
- [ ] Apply security updates
- [ ] Review and rotate access credentials
- [ ] Analyze usage patterns and optimize
- [ ] Hardware maintenance and cleaning

### Quarterly
- [ ] Full system backup and archive
- [ ] Hardware inspection and replacement
- [ ] Performance optimization review
- [ ] Security audit and penetration testing

**🎉 Congratulations! Your self-checkout system is now fully deployed and operational.**

---

## 📞 Support and Contact

For technical support or questions about this deployment:

1. **System Logs**: Always check logs first for error details
2. **Documentation**: Refer to API documentation at `/api-docs`
3. **Hardware Issues**: Check manufacturer documentation
4. **PayFast Support**: Contact PayFast technical support for payment issues

**System is ready for production use! 🚀**
