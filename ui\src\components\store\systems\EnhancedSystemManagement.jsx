import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Alert,
  LinearProgress,
  Avatar,
  Tooltip,
  Tab,
  Tabs,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction
} from '@mui/material';
import {
  Computer,
  Refresh,
  Warning,
  CheckCircle,
  Error,
  Camera,
  Print,
  CreditCard,
  Wifi,
  Memory,
  Storage,
  Analytics,
  TrendingUp,
  TrendingDown,
  DeviceHub,
  MonitorHeart,
  Assessment,
  BarChart,
  Thermostat,
  LocationOn,
  ShoppingCart,
  SettingsRemote,
  Visibility
} from '@mui/icons-material';

const EnhancedSystemManagement = ({ storeId }) => {
  const [systems, setSystems] = useState([]);
  const [systemAnalytics, setSystemAnalytics] = useState({});
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState(0);

  useEffect(() => {
    fetchSystems();
    fetchSystemAnalytics();
    const interval = setInterval(() => {
      fetchSystems();
      fetchSystemAnalytics();
    }, 30000); // Update every 30 seconds
    return () => clearInterval(interval);
  }, [storeId]);

  const fetchSystems = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:5000/api/stores/${storeId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        // Enhanced systems with mock performance data for demonstration
        const enhancedSystems = (data.systems || []).map((system, index) => ({
          ...system,
          id: system._id || index,
          performance: {
            uptime: Math.floor(Math.random() * 100),
            cpuUsage: Math.floor(Math.random() * 100),
            memoryUsage: Math.floor(Math.random() * 100),
            diskUsage: Math.floor(Math.random() * 100),
            temperature: Math.floor(Math.random() * 40) + 30,
            networkStrength: Math.floor(Math.random() * 100),
            lastHeartbeat: new Date(Date.now() - Math.random() * 300000).toISOString(),
            transactionsToday: Math.floor(Math.random() * 50),
            totalTransactions: Math.floor(Math.random() * 1000) + 100,
            averageTransactionTime: Math.floor(Math.random() * 60) + 30,
            errorCount: Math.floor(Math.random() * 5),
            lastError: Math.random() > 0.7 ? 'Camera connection timeout' : null,
            revenue: Math.floor(Math.random() * 5000) + 1000
          },
          hardware: {
            camera: Math.random() > 0.1 ? 'online' : 'offline',
            printer: Math.random() > 0.05 ? 'online' : 'offline',
            scanner: Math.random() > 0.08 ? 'online' : 'offline',
            posTerminal: Math.random() > 0.12 ? 'online' : 'offline',
            touchscreen: Math.random() > 0.02 ? 'online' : 'offline'
          },
          status: Math.random() > 0.1 ? 'online' : 'offline'
        }));
        setSystems(enhancedSystems);
      }
    } catch (error) {
      console.error('Error fetching systems:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchSystemAnalytics = async () => {
    try {
      // Calculate analytics from systems data
      const totalSystems = systems.length;
      const onlineSystems = systems.filter(s => s.status === 'online').length;
      const offlineSystems = totalSystems - onlineSystems;
      const averageUptime = systems.reduce((acc, s) => acc + (s.performance?.uptime || 0), 0) / totalSystems || 0;
      const totalTransactionsToday = systems.reduce((acc, s) => acc + (s.performance?.transactionsToday || 0), 0);
      const averageResponseTime = systems.reduce((acc, s) => acc + (s.performance?.averageTransactionTime || 0), 0) / totalSystems || 0;
      const totalRevenue = systems.reduce((acc, s) => acc + (s.performance?.revenue || 0), 0);

      setSystemAnalytics({
        totalSystems,
        onlineSystems,
        offlineSystems,
        averageUptime: Math.round(averageUptime),
        totalTransactionsToday,
        averageResponseTime: Math.round(averageResponseTime),
        totalRevenue,
        systemAlerts: systems.filter(s => s.performance?.errorCount > 0 || s.status === 'offline')
      });
    } catch (error) {
      console.error('Error calculating system analytics:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'online': return 'success';
      case 'offline': return 'error';
      case 'maintenance': return 'warning';
      default: return 'default';
    }
  };

  const getHardwareStatusIcon = (status) => {
    return status === 'online' ? 
      <CheckCircle color="success" fontSize="small" /> : 
      <Error color="error" fontSize="small" />;
  };

  const getPerformanceColor = (value) => {
    if (value >= 80) return 'error';
    if (value >= 60) return 'warning';
    return 'success';
  };

  const MetricCard = ({ title, value, icon, color = 'primary', format = 'number', suffix = '' }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" color={color}>
              {format === 'currency' ? `R${value?.toLocaleString() || 0}` : 
               format === 'percentage' ? `${value || 0}%` : 
               value?.toLocaleString() || 0}{suffix}
            </Typography>
          </Box>
          <Avatar sx={{ bgcolor: `${color}.light` }}>
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <LinearProgress sx={{ width: '50%' }} />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">System Management</Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={() => {
              fetchSystems();
              fetchSystemAnalytics();
            }}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<Analytics />}
          >
            Generate Report
          </Button>
        </Box>
      </Box>

      {/* System Analytics Overview */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Total Systems"
            value={systemAnalytics?.totalSystems}
            icon={<DeviceHub />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Online Systems"
            value={systemAnalytics?.onlineSystems}
            icon={<CheckCircle />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Offline Systems"
            value={systemAnalytics?.offlineSystems}
            icon={<Error />}
            color="error"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Average Uptime"
            value={systemAnalytics?.averageUptime}
            format="percentage"
            icon={<TrendingUp />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Transactions Today"
            value={systemAnalytics?.totalTransactionsToday}
            icon={<ShoppingCart />}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={2}>
          <MetricCard
            title="Total Revenue"
            value={systemAnalytics?.totalRevenue}
            format="currency"
            icon={<Assessment />}
            color="success"
          />
        </Grid>
      </Grid>

      {/* System Alerts */}
      {systemAnalytics?.systemAlerts?.length > 0 && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="body2">
            <strong>System Alerts:</strong> {systemAnalytics.systemAlerts.length} systems require attention.
          </Typography>
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Card>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab 
            label="System Overview" 
            icon={<MonitorHeart />} 
            iconPosition="start"
          />
          <Tab 
            label="Performance" 
            icon={<BarChart />} 
            iconPosition="start"
          />
          <Tab 
            label="Hardware Status" 
            icon={<Computer />} 
            iconPosition="start"
          />
          <Tab 
            label="Analytics" 
            icon={<Analytics />} 
            iconPosition="start"
          />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {/* System Overview Tab */}
          {activeTab === 0 && (
            <Box>
              <Typography variant="h6" mb={3}>System Overview</Typography>
              
              <Grid container spacing={3}>
                {systems.map((system) => (
                  <Grid item xs={12} md={6} lg={4} key={system.id}>
                    <Card>
                      <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                          <Box display="flex" alignItems="center">
                            <Avatar
                              sx={{
                                bgcolor: getStatusColor(system.status) + '.main',
                                mr: 2
                              }}
                            >
                              <Computer />
                            </Avatar>
                            <Box>
                              <Typography variant="h6">{system.name || system.location}</Typography>
                              <Typography variant="body2" color="textSecondary">
                                {system.systemId}
                              </Typography>
                            </Box>
                          </Box>
                          <Chip 
                            label={system.status} 
                            color={getStatusColor(system.status)}
                            size="small"
                          />
                        </Box>
                        
                        <Box mb={2}>
                          <Typography variant="body2" color="textSecondary">Location</Typography>
                          <Typography variant="body2">{system.location}</Typography>
                        </Box>
                        
                        <Grid container spacing={2} mb={2}>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="textSecondary">Transactions Today</Typography>
                            <Typography variant="h6">{system.performance?.transactionsToday || 0}</Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="textSecondary">Revenue</Typography>
                            <Typography variant="h6">R{system.performance?.revenue?.toLocaleString() || 0}</Typography>
                          </Grid>
                        </Grid>
                        
                        <Box display="flex" gap={1}>
                          <Button size="small" startIcon={<Visibility />}>View Details</Button>
                          <Button size="small" startIcon={<SettingsRemote />}>Remote Control</Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* Performance Tab */}
          {activeTab === 1 && (
            <Box>
              <Typography variant="h6" mb={3}>System Performance</Typography>

              <TableContainer component={Paper}>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>System</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell align="right">Uptime</TableCell>
                      <TableCell align="right">CPU Usage</TableCell>
                      <TableCell align="right">Memory Usage</TableCell>
                      <TableCell align="right">Disk Usage</TableCell>
                      <TableCell align="right">Temperature</TableCell>
                      <TableCell align="right">Network</TableCell>
                      <TableCell>Last Heartbeat</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {systems.map((system) => (
                      <TableRow key={system.id}>
                        <TableCell>
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {system.name || system.location}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              {system.systemId}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={system.status}
                            color={getStatusColor(system.status)}
                            size="small"
                          />
                        </TableCell>
                        <TableCell align="right">
                          <Box>
                            <Typography variant="body2">{system.performance?.uptime || 0}%</Typography>
                            <LinearProgress
                              variant="determinate"
                              value={system.performance?.uptime || 0}
                              color={getPerformanceColor(100 - (system.performance?.uptime || 0))}
                              sx={{ width: 60, height: 4 }}
                            />
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          <Box>
                            <Typography variant="body2">{system.performance?.cpuUsage || 0}%</Typography>
                            <LinearProgress
                              variant="determinate"
                              value={system.performance?.cpuUsage || 0}
                              color={getPerformanceColor(system.performance?.cpuUsage || 0)}
                              sx={{ width: 60, height: 4 }}
                            />
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          <Box>
                            <Typography variant="body2">{system.performance?.memoryUsage || 0}%</Typography>
                            <LinearProgress
                              variant="determinate"
                              value={system.performance?.memoryUsage || 0}
                              color={getPerformanceColor(system.performance?.memoryUsage || 0)}
                              sx={{ width: 60, height: 4 }}
                            />
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          <Box>
                            <Typography variant="body2">{system.performance?.diskUsage || 0}%</Typography>
                            <LinearProgress
                              variant="determinate"
                              value={system.performance?.diskUsage || 0}
                              color={getPerformanceColor(system.performance?.diskUsage || 0)}
                              sx={{ width: 60, height: 4 }}
                            />
                          </Box>
                        </TableCell>
                        <TableCell align="right">
                          <Typography
                            variant="body2"
                            color={system.performance?.temperature > 70 ? 'error' : 'textPrimary'}
                          >
                            {system.performance?.temperature || 0}°C
                          </Typography>
                        </TableCell>
                        <TableCell align="right">
                          <Box display="flex" alignItems="center" justifyContent="flex-end">
                            <Typography variant="body2" sx={{ mr: 1 }}>
                              {system.performance?.networkStrength || 0}%
                            </Typography>
                            {system.performance?.networkStrength > 70 ?
                              <Wifi color="success" fontSize="small" /> :
                              <Wifi color="error" fontSize="small" />
                            }
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Typography variant="caption">
                            {system.performance?.lastHeartbeat ?
                              new Date(system.performance.lastHeartbeat).toLocaleString() :
                              'Never'
                            }
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}

          {/* Hardware Status Tab */}
          {activeTab === 2 && (
            <Box>
              <Typography variant="h6" mb={3}>Hardware Status</Typography>

              <Grid container spacing={3}>
                {systems.map((system) => (
                  <Grid item xs={12} md={6} key={system.id}>
                    <Card>
                      <CardContent>
                        <Typography variant="h6" gutterBottom>
                          {system.name || system.location}
                        </Typography>
                        <Typography variant="body2" color="textSecondary" mb={2}>
                          {system.systemId}
                        </Typography>

                        <List dense>
                          <ListItem>
                            <ListItemIcon>
                              <Camera />
                            </ListItemIcon>
                            <ListItemText primary="Camera" />
                            <ListItemSecondaryAction>
                              {getHardwareStatusIcon(system.hardware?.camera)}
                            </ListItemSecondaryAction>
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <Print />
                            </ListItemIcon>
                            <ListItemText primary="Printer" />
                            <ListItemSecondaryAction>
                              {getHardwareStatusIcon(system.hardware?.printer)}
                            </ListItemSecondaryAction>
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <SettingsRemote />
                            </ListItemIcon>
                            <ListItemText primary="Scanner" />
                            <ListItemSecondaryAction>
                              {getHardwareStatusIcon(system.hardware?.scanner)}
                            </ListItemSecondaryAction>
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <CreditCard />
                            </ListItemIcon>
                            <ListItemText primary="POS Terminal" />
                            <ListItemSecondaryAction>
                              {getHardwareStatusIcon(system.hardware?.posTerminal)}
                            </ListItemSecondaryAction>
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <Computer />
                            </ListItemIcon>
                            <ListItemText primary="Touchscreen" />
                            <ListItemSecondaryAction>
                              {getHardwareStatusIcon(system.hardware?.touchscreen)}
                            </ListItemSecondaryAction>
                          </ListItem>
                        </List>

                        {system.performance?.lastError && (
                          <Alert severity="warning" sx={{ mt: 2 }}>
                            <Typography variant="body2">
                              Last Error: {system.performance.lastError}
                            </Typography>
                          </Alert>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* Analytics Tab */}
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" mb={3}>System Analytics</Typography>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Transaction Performance</Typography>
                      <TableContainer>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>System</TableCell>
                              <TableCell align="right">Today</TableCell>
                              <TableCell align="right">Total</TableCell>
                              <TableCell align="right">Avg Time</TableCell>
                              <TableCell align="right">Errors</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {systems.map((system) => (
                              <TableRow key={system.id}>
                                <TableCell>{system.name || system.location}</TableCell>
                                <TableCell align="right">{system.performance?.transactionsToday || 0}</TableCell>
                                <TableCell align="right">{system.performance?.totalTransactions || 0}</TableCell>
                                <TableCell align="right">{system.performance?.averageTransactionTime || 0}s</TableCell>
                                <TableCell align="right">
                                  <Typography
                                    variant="body2"
                                    color={system.performance?.errorCount > 0 ? 'error' : 'textPrimary'}
                                  >
                                    {system.performance?.errorCount || 0}
                                  </Typography>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Revenue by System</Typography>
                      <TableContainer>
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell>System</TableCell>
                              <TableCell align="right">Revenue</TableCell>
                              <TableCell align="right">Transactions</TableCell>
                              <TableCell align="right">Avg per Transaction</TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {systems.map((system) => {
                              const avgPerTransaction = system.performance?.transactionsToday > 0 ?
                                (system.performance?.revenue || 0) / system.performance.transactionsToday : 0;
                              return (
                                <TableRow key={system.id}>
                                  <TableCell>{system.name || system.location}</TableCell>
                                  <TableCell align="right">R{(system.performance?.revenue || 0).toLocaleString()}</TableCell>
                                  <TableCell align="right">{system.performance?.transactionsToday || 0}</TableCell>
                                  <TableCell align="right">R{avgPerTransaction.toFixed(2)}</TableCell>
                                </TableRow>
                              );
                            })}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}
        </Box>
      </Card>
    </Box>
  );
};

export default EnhancedSystemManagement;
