#!/bin/bash

# Self-Checkout System - Raspberry Pi Setup Script
# Compatible with: Raspberry Pi 4 Model B
# Hardware: 7" Touchscreen, Camera Module 3 NOIR, Angel Q-A202 Scanner, Thermal Printer, PayFast POS

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[WARNING] $1${NC}"
}

error() {
    echo -e "${RED}[ERROR] $1${NC}"
}

info() {
    echo -e "${BLUE}[INFO] $1${NC}"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   error "This script should not be run as root. Please run as the 'pi' user."
   exit 1
fi

log "🍓 Starting Raspberry Pi Self-Checkout System Setup"
log "Hardware: Pi 4B + 7\" Touchscreen + Camera Module 3 NOIR + Angel Q-A202 + Thermal Printer"

# Update system
log "📦 Updating system packages..."
sudo apt update && sudo apt upgrade -y

# Install essential packages
log "🔧 Installing essential packages..."
sudo apt install -y \
    python3-pip \
    python3-venv \
    python3-dev \
    python3-opencv \
    python3-tk \
    git \
    curl \
    wget \
    unzip \
    build-essential \
    cmake \
    pkg-config \
    libjpeg-dev \
    libtiff5-dev \
    libjasper-dev \
    libpng-dev \
    libavcodec-dev \
    libavformat-dev \
    libswscale-dev \
    libv4l-dev \
    libxvidcore-dev \
    libx264-dev \
    libfontconfig1-dev \
    libcairo2-dev \
    libgdk-pixbuf2.0-dev \
    libpango1.0-dev \
    libgtk2.0-dev \
    libgtk-3-dev \
    libatlas-base-dev \
    gfortran \
    libhdf5-dev \
    libhdf5-serial-dev \
    libhdf5-103 \
    libqtgui4 \
    libqtwebkit4 \
    libqt4-test \
    python3-pyqt5 \
    libzbar0 \
    libzbar-dev

# Install printer support (CUPS)
log "🖨️  Installing printer support (CUPS)..."
sudo apt install -y cups cups-client cups-bsd
sudo usermod -a -G lpadmin pi

# Install USB device support
log "🔌 Installing USB device support..."
sudo apt install -y python3-evdev

# Enable hardware interfaces
log "📷 Enabling hardware interfaces..."
sudo raspi-config nonint do_camera 0      # Enable camera
sudo raspi-config nonint do_spi 0         # Enable SPI
sudo raspi-config nonint do_i2c 0         # Enable I2C
sudo raspi-config nonint do_ssh 0         # Enable SSH

# Configure display settings for 7" touchscreen
log "📺 Configuring display settings for 7\" touchscreen..."
if ! grep -q "hdmi_force_hotplug=1" /boot/config.txt; then
    echo "hdmi_force_hotplug=1" | sudo tee -a /boot/config.txt
fi

if ! grep -q "hdmi_drive=2" /boot/config.txt; then
    echo "hdmi_drive=2" | sudo tee -a /boot/config.txt
fi

# Disable screen blanking for kiosk mode
log "💡 Disabling screen blanking for kiosk mode..."
sudo sed -i 's/#xserver-command=X/xserver-command=X -s 0 dpms/' /etc/lightdm/lightdm.conf 2>/dev/null || true

# Configure automatic login
log "🔐 Configuring automatic login..."
sudo raspi-config nonint do_boot_behaviour B4  # Desktop autologin

# Create project directory
PROJECT_DIR="$HOME/selfcheckout"
log "📁 Creating project directory: $PROJECT_DIR"
mkdir -p "$PROJECT_DIR"
cd "$PROJECT_DIR"

# Create Python virtual environment
log "🐍 Creating Python virtual environment..."
python3 -m venv selfcheckout_env

# Activate virtual environment
source selfcheckout_env/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install Python packages
log "📦 Installing Python packages..."
pip install \
    opencv-python==******** \
    pyzbar==0.1.9 \
    ttkbootstrap==1.10.1 \
    requests==2.31.0 \
    pillow==10.0.1 \
    numpy==1.24.3 \
    evdev==1.6.1

# Test camera functionality
log "🔍 Testing camera functionality..."
if python3 -c "
import cv2
cap = cv2.VideoCapture(0)
if cap.isOpened():
    print('✅ Camera test successful')
    cap.release()
else:
    print('❌ Camera test failed')
    exit(1)
" 2>/dev/null; then
    log "✅ Camera test passed"
else
    warn "⚠️  Camera test failed. Camera will be configured during first run."
fi

# Test barcode scanning libraries
log "📊 Testing barcode scanning libraries..."
python3 -c "
import pyzbar
import cv2
print('✅ Barcode scanning libraries installed successfully')
" 2>/dev/null && log "✅ Barcode libraries test passed" || warn "⚠️  Barcode libraries test failed"

# Test USB device access
log "🔌 Testing USB device access..."
python3 -c "
import evdev
devices = evdev.list_devices()
print(f'✅ Found {len(devices)} USB devices')
" 2>/dev/null && log "✅ USB device access test passed" || warn "⚠️  USB device access test failed"

# Create startup script
log "🚀 Creating startup script..."
cat > start_selfcheckout.sh << 'EOF'
#!/bin/bash
cd /home/<USER>/selfcheckout
source selfcheckout_env/bin/activate

# Wait for network
while ! ping -c 1 google.com &> /dev/null; do
    echo "Waiting for network connection..."
    sleep 5
done

# Start the application
python sys2.py
EOF

chmod +x start_selfcheckout.sh

# Create stop script
cat > stop_selfcheckout.sh << 'EOF'
#!/bin/bash
sudo systemctl stop selfcheckout.service
EOF

chmod +x stop_selfcheckout.sh

# Create systemd service for auto-start
log "⚙️  Creating systemd service..."
sudo tee /etc/systemd/system/selfcheckout.service > /dev/null <<EOF
[Unit]
Description=Self Checkout System
After=network.target graphical-session.target
Wants=graphical-session.target

[Service]
Type=simple
User=pi
Group=pi
WorkingDirectory=/home/<USER>/selfcheckout
Environment=DISPLAY=:0
Environment=XAUTHORITY=/home/<USER>/.Xauthority
ExecStartPre=/bin/sleep 10
ExecStart=/home/<USER>/selfcheckout/start_selfcheckout.sh
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=graphical.target
EOF

# Enable the service (but don't start it yet)
sudo systemctl daemon-reload
sudo systemctl enable selfcheckout.service

log "✅ Raspberry Pi setup completed successfully!"
echo ""
info "📋 Next steps:"
info "1. Copy sys2.py and system_config.json to $PROJECT_DIR"
info "2. Edit system_config.json with your store configuration"
info "3. Configure your backend server URL in system_config.json"
info "4. Set up thermal printer (see printer setup guide below)"
info "5. Test the system: ./start_selfcheckout.sh"
info "6. Enable auto-start: sudo systemctl start selfcheckout.service"
echo ""
info "🖨️  Thermal Printer Setup:"
info "1. Connect thermal printer via USB"
info "2. Run: sudo lpadmin -p ReceiptPrinter -E -v usb://your-printer-uri -m raw"
info "3. Set as default: sudo lpoptions -d ReceiptPrinter"
info "4. Test: echo 'Test print' | lp -d ReceiptPrinter"
echo ""
info "📱 PayFast POS Setup:"
info "1. Connect PayFast terminal via USB/Bluetooth"
info "2. Configure through PayFast merchant dashboard"
info "3. Test connection with PayFast support"
echo ""
info "🔧 Configuration files:"
info "- System config: $PROJECT_DIR/system_config.json"
info "- Startup script: $PROJECT_DIR/start_selfcheckout.sh"
info "- Service: /etc/systemd/system/selfcheckout.service"
echo ""
info "📊 Monitoring:"
info "- View logs: journalctl -u selfcheckout.service -f"
info "- Check status: systemctl status selfcheckout.service"
echo ""
warn "⚠️  Remember to reboot after setup to ensure all hardware interfaces are properly initialized!"
