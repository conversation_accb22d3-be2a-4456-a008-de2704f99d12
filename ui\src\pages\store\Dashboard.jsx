import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Grid, 
  Card, 
  CardContent, 
  Button,
  CircularProgress,
  Tab,
  Tabs,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Analytics,
  Inventory,
  AttachMoney,
  People,
  Computer,
  Refresh,
  Settings,
  Campaign,
  Warehouse,
  Insights,
  Security,
  Receipt
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import AnalyticsDashboard from '../../components/store/analytics/AnalyticsDashboard';
import AdvancedProductManagement from '../../components/store/products/AdvancedProductManagement';
import FinancialManagement from '../../components/store/financial/FinancialManagement';
import CustomerAnalytics from '../../components/store/customer/CustomerAnalytics';
import EnhancedCustomerAnalytics from '../../components/store/customer/EnhancedCustomerAnalytics';
import SystemManagement from '../../components/store/systems/EnhancedSystemManagement';
import EnhancedSystemAnalytics from '../../components/store/systems/EnhancedSystemAnalytics';
import MarketingTools from '../../components/store/marketing/MarketingTools';
import InventoryManagement from '../../components/store/inventory/InventoryManagement';
import BusinessIntelligence from '../../components/store/business-intelligence/BusinessIntelligence';
import SecurityCompliance from '../../components/store/security/SecurityCompliance';
import ReceiptTemplateManager from '../../components/store/receipt/ReceiptTemplateManager';
import EnhancedReceiptTemplateManager from '../../components/store/receipt/EnhancedReceiptTemplateManager';

const StoreDashboard = () => {
  const [storeData, setStoreData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState(0);
  const navigate = useNavigate();

  useEffect(() => {
    fetchStoreData();
  }, []);

  const fetchStoreData = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:5000/api/stores', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setStoreData(data[0]); // Assuming first store for now
      }
    } catch (error) {
      console.error('Error fetching store data:', error);
    } finally {
      setLoading(false);
    }
  };

  const QuickStatsCard = ({ title, value, subtitle, icon, color = 'primary' }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" color={color}>
              {value}
            </Typography>
            {subtitle && (
              <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                {subtitle}
              </Typography>
            )}
          </Box>
          <Box
            sx={{
              backgroundColor: `${color}.light`,
              borderRadius: 2,
              p: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" height="400px">
        <CircularProgress />
      </Box>
    );
  }

  const storeId = storeData?._id;

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" gutterBottom>
            {storeData?.name || 'Store Dashboard'}
          </Typography>
          <Box display="flex" alignItems="center" gap={2}>
            <Chip 
              label={`${storeData?.systems?.length || 0} Systems`} 
              color="primary" 
              size="small" 
            />
            <Chip 
              label={storeData?.payfast?.isActive ? "PayFast Enabled" : "PayFast Disabled"} 
              color={storeData?.payfast?.isActive ? "success" : "error"} 
              size="small" 
            />
          </Box>
        </Box>
        <Box display="flex" gap={2}>
          <Tooltip title="Refresh Data">
            <IconButton onClick={fetchStoreData}>
              <Refresh />
            </IconButton>
          </Tooltip>
          <Button
            variant="outlined"
            startIcon={<Settings />}
            onClick={() => navigate('/store/settings')}
          >
            Settings
          </Button>
        </Box>
      </Box>

      {/* Quick Stats */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <QuickStatsCard
            title="Today's Revenue"
            value="R2,847"
            subtitle="+12% vs yesterday"
            icon={<AttachMoney />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <QuickStatsCard
            title="Transactions"
            value="156"
            subtitle="Last 24 hours"
            icon={<DashboardIcon />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <QuickStatsCard
            title="Active Systems"
            value={storeData?.systems?.filter(s => s.status === 'Active').length || 0}
            subtitle="Online now"
            icon={<Computer />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <QuickStatsCard
            title="Products"
            value="1,247"
            subtitle="In catalog"
            icon={<Inventory />}
            color="warning"
          />
        </Grid>
      </Grid>

      {/* Main Content Tabs */}
      <Card>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab 
            label="Analytics" 
            icon={<Analytics />} 
            iconPosition="start"
          />
          <Tab 
            label="Products" 
            icon={<Inventory />} 
            iconPosition="start"
          />
          <Tab 
            label="Financial" 
            icon={<AttachMoney />} 
            iconPosition="start"
          />
          <Tab 
            label="Customers" 
            icon={<People />} 
            iconPosition="start"
          />
          <Tab
            label="Systems"
            icon={<Computer />}
            iconPosition="start"
          />
          <Tab
            label="Marketing"
            icon={<Campaign />}
            iconPosition="start"
          />
          <Tab
            label="Inventory"
            icon={<Warehouse />}
            iconPosition="start"
          />
          <Tab
            label="Business Intelligence"
            icon={<Insights />}
            iconPosition="start"
          />
          <Tab
            label="Security & Compliance"
            icon={<Security />}
            iconPosition="start"
          />
          <Tab
            label="Receipt Templates"
            icon={<Receipt />}
            iconPosition="start"
          />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {activeTab === 0 && storeId && (
            <AnalyticsDashboard storeId={storeId} />
          )}
          {activeTab === 1 && storeId && (
            <AdvancedProductManagement storeId={storeId} />
          )}
          {activeTab === 2 && storeId && (
            <FinancialManagement storeId={storeId} />
          )}
          {activeTab === 3 && storeId && (
            <EnhancedCustomerAnalytics storeId={storeId} />
          )}
          {activeTab === 4 && storeId && (
            <Box>
              <SystemManagement storeId={storeId} />
              <Box mt={4}>
                <EnhancedSystemAnalytics storeId={storeId} />
              </Box>
            </Box>
          )}
          {activeTab === 5 && storeId && (
            <MarketingTools storeId={storeId} />
          )}
          {activeTab === 6 && storeId && (
            <InventoryManagement storeId={storeId} />
          )}
          {activeTab === 7 && storeId && (
            <BusinessIntelligence storeId={storeId} />
          )}
          {activeTab === 8 && storeId && (
            <SecurityCompliance storeId={storeId} />
          )}
          {activeTab === 9 && storeId && (
            <EnhancedReceiptTemplateManager storeId={storeId} />
          )}
        </Box>
      </Card>
    </Box>
  );
};

export default StoreDashboard;
