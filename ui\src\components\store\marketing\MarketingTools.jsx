import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Alert,
  IconButton,
  Menu,
  MenuItem,
  Tooltip,
  Badge,
  LinearProgress,
  Avatar,
  Divider,
  Tab,
  Tabs,
  FormControl,
  InputLabel,
  Select,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Campaign,
  Discount,
  Loyalty,
  Analytics,
  Add,
  Edit,
  Delete,
  Visibility,
  ExpandMore,
  TrendingUp,
  TrendingDown,
  People,
  AttachMoney,
  ShoppingCart,
  Email,
  Sms,
  Notifications,
  Schedule,
  Target,
  Star,
  Gift,
  LocalOffer,
  BarChart,
  PieChart,
  Timeline,
  Refresh
} from '@mui/icons-material';
import { DataGrid } from '@mui/x-data-grid';

const MarketingTools = ({ storeId }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [campaigns, setCampaigns] = useState([]);
  const [promotions, setPromotions] = useState([]);
  const [loyaltyPrograms, setLoyaltyPrograms] = useState([]);
  const [marketingAnalytics, setMarketingAnalytics] = useState(null);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogType, setDialogType] = useState('');
  const [selectedItem, setSelectedItem] = useState(null);

  useEffect(() => {
    fetchMarketingData();
  }, [storeId]);

  const fetchMarketingData = async () => {
    try {
      setLoading(true);
      // Mock data for demonstration
      setCampaigns([
        {
          id: 1,
          name: 'Summer Sale 2024',
          type: 'email',
          status: 'active',
          startDate: '2024-06-01',
          endDate: '2024-08-31',
          reach: 1250,
          engagement: 18.5,
          conversions: 89,
          revenue: 15420
        },
        {
          id: 2,
          name: 'Back to School',
          type: 'sms',
          status: 'scheduled',
          startDate: '2024-08-15',
          endDate: '2024-09-15',
          reach: 850,
          engagement: 0,
          conversions: 0,
          revenue: 0
        }
      ]);

      setPromotions([
        {
          id: 1,
          name: '20% Off Electronics',
          type: 'percentage',
          value: 20,
          category: 'Electronics',
          status: 'active',
          startDate: '2024-07-01',
          endDate: '2024-07-31',
          usageCount: 156,
          maxUsage: 500
        },
        {
          id: 2,
          name: 'Buy 2 Get 1 Free',
          type: 'bogo',
          category: 'Groceries',
          status: 'active',
          startDate: '2024-07-10',
          endDate: '2024-07-20',
          usageCount: 89,
          maxUsage: 200
        }
      ]);

      setLoyaltyPrograms([
        {
          id: 1,
          name: 'VIP Rewards',
          type: 'points',
          pointsPerRand: 1,
          rewardThreshold: 100,
          activeMembers: 342,
          totalRewards: 1250,
          status: 'active'
        }
      ]);

      setMarketingAnalytics({
        totalCampaigns: 12,
        activeCampaigns: 3,
        totalReach: 15420,
        avgEngagement: 16.8,
        totalConversions: 234,
        marketingROI: 3.2,
        customerAcquisitionCost: 45.50,
        lifetimeValue: 890.25
      });

    } catch (error) {
      console.error('Error fetching marketing data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateCampaign = () => {
    setDialogType('campaign');
    setSelectedItem(null);
    setOpenDialog(true);
  };

  const handleCreatePromotion = () => {
    setDialogType('promotion');
    setSelectedItem(null);
    setOpenDialog(true);
  };

  const handleCreateLoyaltyProgram = () => {
    setDialogType('loyalty');
    setSelectedItem(null);
    setOpenDialog(true);
  };

  const MetricCard = ({ title, value, change, icon, color = 'primary', format = 'number' }) => (
    <Card sx={{ height: '100%' }}>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box>
            <Typography color="textSecondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h4" component="div" color={color}>
              {format === 'currency' ? `R${value?.toLocaleString() || 0}` : 
               format === 'percentage' ? `${value || 0}%` : 
               value?.toLocaleString() || 0}
            </Typography>
            {change !== undefined && (
              <Box display="flex" alignItems="center" mt={1}>
                {change >= 0 ? (
                  <TrendingUp color="success" fontSize="small" />
                ) : (
                  <TrendingDown color="error" fontSize="small" />
                )}
                <Typography
                  variant="body2"
                  color={change >= 0 ? 'success.main' : 'error.main'}
                  sx={{ ml: 0.5 }}
                >
                  {Math.abs(change)}%
                </Typography>
              </Box>
            )}
          </Box>
          <Avatar sx={{ bgcolor: `${color}.light` }}>
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <LinearProgress sx={{ width: '50%' }} />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Marketing Tools</Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={fetchMarketingData}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleCreateCampaign}
          >
            New Campaign
          </Button>
        </Box>
      </Box>

      {/* Marketing Analytics Overview */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Campaigns"
            value={marketingAnalytics?.totalCampaigns}
            icon={<Campaign />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Marketing ROI"
            value={marketingAnalytics?.marketingROI}
            format="number"
            icon={<TrendingUp />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Reach"
            value={marketingAnalytics?.totalReach}
            icon={<People />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Avg Engagement"
            value={marketingAnalytics?.avgEngagement}
            format="percentage"
            icon={<Analytics />}
            color="warning"
          />
        </Grid>
      </Grid>

      {/* Main Content Tabs */}
      <Card>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab 
            label="Campaigns" 
            icon={<Campaign />} 
            iconPosition="start"
          />
          <Tab 
            label="Promotions" 
            icon={<LocalOffer />} 
            iconPosition="start"
          />
          <Tab 
            label="Loyalty Programs" 
            icon={<Star />} 
            iconPosition="start"
          />
          <Tab 
            label="Analytics" 
            icon={<BarChart />} 
            iconPosition="start"
          />
        </Tabs>

        <Box sx={{ p: 3 }}>
          {/* Campaigns Tab */}
          {activeTab === 0 && (
            <Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6">Marketing Campaigns</Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={handleCreateCampaign}
                >
                  Create Campaign
                </Button>
              </Box>

              <Grid container spacing={3}>
                {campaigns.map((campaign) => (
                  <Grid item xs={12} md={6} key={campaign.id}>
                    <Card>
                      <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                          <Typography variant="h6">{campaign.name}</Typography>
                          <Chip
                            label={campaign.status}
                            color={campaign.status === 'active' ? 'success' : 'default'}
                            size="small"
                          />
                        </Box>

                        <Box display="flex" alignItems="center" mb={1}>
                          {campaign.type === 'email' ? <Email fontSize="small" /> : <Sms fontSize="small" />}
                          <Typography variant="body2" sx={{ ml: 1 }}>
                            {campaign.type.toUpperCase()} Campaign
                          </Typography>
                        </Box>

                        <Typography variant="body2" color="textSecondary" mb={2}>
                          {campaign.startDate} - {campaign.endDate}
                        </Typography>

                        <Grid container spacing={2}>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="textSecondary">Reach</Typography>
                            <Typography variant="h6">{campaign.reach.toLocaleString()}</Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="textSecondary">Engagement</Typography>
                            <Typography variant="h6">{campaign.engagement}%</Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="textSecondary">Conversions</Typography>
                            <Typography variant="h6">{campaign.conversions}</Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="textSecondary">Revenue</Typography>
                            <Typography variant="h6">R{campaign.revenue.toLocaleString()}</Typography>
                          </Grid>
                        </Grid>

                        <Box display="flex" gap={1} mt={2}>
                          <Button size="small" startIcon={<Edit />}>Edit</Button>
                          <Button size="small" startIcon={<Visibility />}>View</Button>
                          <Button size="small" startIcon={<Analytics />}>Analytics</Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* Promotions Tab */}
          {activeTab === 1 && (
            <Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6">Promotions & Discounts</Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={handleCreatePromotion}
                >
                  Create Promotion
                </Button>
              </Box>

              <Grid container spacing={3}>
                {promotions.map((promotion) => (
                  <Grid item xs={12} md={6} key={promotion.id}>
                    <Card>
                      <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                          <Typography variant="h6">{promotion.name}</Typography>
                          <Chip
                            label={promotion.status}
                            color={promotion.status === 'active' ? 'success' : 'default'}
                            size="small"
                          />
                        </Box>

                        <Box display="flex" alignItems="center" mb={1}>
                          <LocalOffer fontSize="small" />
                          <Typography variant="body2" sx={{ ml: 1 }}>
                            {promotion.type === 'percentage' ? `${promotion.value}% Off` :
                             promotion.type === 'bogo' ? 'Buy 2 Get 1 Free' : promotion.type}
                          </Typography>
                        </Box>

                        <Typography variant="body2" color="textSecondary" mb={1}>
                          Category: {promotion.category}
                        </Typography>

                        <Typography variant="body2" color="textSecondary" mb={2}>
                          {promotion.startDate} - {promotion.endDate}
                        </Typography>

                        <Box mb={2}>
                          <Box display="flex" justifyContent="space-between" mb={1}>
                            <Typography variant="body2">Usage</Typography>
                            <Typography variant="body2">
                              {promotion.usageCount}/{promotion.maxUsage}
                            </Typography>
                          </Box>
                          <LinearProgress
                            variant="determinate"
                            value={(promotion.usageCount / promotion.maxUsage) * 100}
                          />
                        </Box>

                        <Box display="flex" gap={1}>
                          <Button size="small" startIcon={<Edit />}>Edit</Button>
                          <Button size="small" startIcon={<Analytics />}>Analytics</Button>
                          <Button size="small" startIcon={<Delete />} color="error">Delete</Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* Loyalty Programs Tab */}
          {activeTab === 2 && (
            <Box>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                <Typography variant="h6">Loyalty Programs</Typography>
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={handleCreateLoyaltyProgram}
                >
                  Create Program
                </Button>
              </Box>

              <Grid container spacing={3}>
                {loyaltyPrograms.map((program) => (
                  <Grid item xs={12} md={6} key={program.id}>
                    <Card>
                      <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                          <Typography variant="h6">{program.name}</Typography>
                          <Chip
                            label={program.status}
                            color={program.status === 'active' ? 'success' : 'default'}
                            size="small"
                          />
                        </Box>

                        <Box display="flex" alignItems="center" mb={2}>
                          <Star fontSize="small" />
                          <Typography variant="body2" sx={{ ml: 1 }}>
                            {program.pointsPerRand} point per R1 spent
                          </Typography>
                        </Box>

                        <Grid container spacing={2} mb={2}>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="textSecondary">Active Members</Typography>
                            <Typography variant="h6">{program.activeMembers.toLocaleString()}</Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="textSecondary">Total Rewards</Typography>
                            <Typography variant="h6">{program.totalRewards.toLocaleString()}</Typography>
                          </Grid>
                        </Grid>

                        <Typography variant="body2" color="textSecondary" mb={2}>
                          Reward Threshold: {program.rewardThreshold} points
                        </Typography>

                        <Box display="flex" gap={1}>
                          <Button size="small" startIcon={<Edit />}>Edit</Button>
                          <Button size="small" startIcon={<People />}>Members</Button>
                          <Button size="small" startIcon={<Analytics />}>Analytics</Button>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          )}

          {/* Analytics Tab */}
          {activeTab === 3 && (
            <Box>
              <Typography variant="h6" mb={3}>Marketing Analytics</Typography>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Customer Acquisition</Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="textSecondary">Cost per Acquisition</Typography>
                          <Typography variant="h5">R{marketingAnalytics?.customerAcquisitionCost}</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="textSecondary">Lifetime Value</Typography>
                          <Typography variant="h5">R{marketingAnalytics?.lifetimeValue}</Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Card>
                    <CardContent>
                      <Typography variant="h6" gutterBottom>Campaign Performance</Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="textSecondary">Total Conversions</Typography>
                          <Typography variant="h5">{marketingAnalytics?.totalConversions}</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="textSecondary">Active Campaigns</Typography>
                          <Typography variant="h5">{marketingAnalytics?.activeCampaigns}</Typography>
                        </Grid>
                      </Grid>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          )}
        </Box>
      </Card>
    </Box>
  );
};

export default MarketingTools;
